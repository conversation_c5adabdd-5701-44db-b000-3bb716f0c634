#!/usr/bin/env python3

# Simple test script to debug the TestClient issue
import sys
from pathlib import Path

try:
    from fastapi.testclient import TestClient
    from main import app
    
    print("✓ Successfully imported FastAPI TestClient and main app")
    
    # Try different ways to create TestClient
    try:
        client = TestClient(app)
        print("✓ TestClient(app) works")
    except Exception as e:
        print(f"✗ TestClient(app) failed: {e}")

        try:
            # Try with explicit parameter name
            client = TestClient(app=app)
            print("✓ TestClient(app=app) works")
        except Exception as e2:
            print(f"✗ TestClient(app=app) failed: {e2}")

            try:
                # Try importing from httpx directly
                import httpx
                client = httpx.Client(app=app, base_url="http://testserver")
                print("✓ httpx.Client works")
            except Exception as e3:
                print(f"✗ httpx.Client failed: {e3}")

                try:
                    # Try the direct approach
                    from starlette.testclient import TestClient as DirectTestClient
                    client = DirectTestClient(app)
                    print("✓ Direct TestClient works")
                except Exception as e4:
                    print(f"✗ Direct TestClient failed: {e4}")
    
    # Test a simple request
    if 'client' in locals():
        try:
            response = client.get("/")
            print(f"✓ GET / returned status: {response.status_code}")
            print(f"  Response: {response.json()}")
        except Exception as e:
            print(f"✗ GET / failed: {e}")
    
except ImportError as e:
    print(f"✗ Import failed: {e}")
    sys.exit(1)
